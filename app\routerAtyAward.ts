/*
 * @Description: 运营活动配置中心
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-16 16:45:57
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【运营活动】-奖励配置
    //活动列表
    router.post(`${API_VERSION}/werewolf/atyAward/atyList`,controller.werewolf.activityAward.getActivityIndex)
    //活动的奖励组列表
    router.post(`${API_VERSION}/werewolf/atyAward/groupList`,controller.werewolf.activityAward.getActivityAwardGroup)
    //活动的奖励列表
    router.post(`${API_VERSION}/werewolf/atyAward/confList`,controller.werewolf.activityAward.getActivityConf)

    router.post(`${API_VERSION}/werewolf/atyAward/insertAwardGroup`,controller.werewolf.activityAward.insertAwardGroup)

    router.post(`${API_VERSION}/werewolf/atyAward/updateAwardGroup`,controller.werewolf.activityAward.updateAwardGroup)

    router.post(`${API_VERSION}/werewolf/atyAward/insertAward`,controller.werewolf.activityAward.insertAward)

    router.post(`${API_VERSION}/werewolf/atyAward/subItemList`, controller.werewolf.activityAward.getSubItemList);
}

export default load
