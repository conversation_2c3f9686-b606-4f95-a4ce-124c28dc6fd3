/*
 * @Description: 飞书机器人路由配置
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: AI Assistant
 * @Date: 2023-11-22 10:00:00
 * @LastEditors: AI Assistant
 * @LastEditTime: 2023-11-22 11:00:00
 */

import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    
    // 【飞书机器人】发送消息
    router.post(`${API_VERSION}/werewolf/feiShu/sendMessage`, accCtr(AccessRouteId.wolf_push), controller.werewolf.feiShu.sendMessage);
    
    // 【飞书机器人】测试连接
    router.get(`${API_VERSION}/werewolf/feiShu/test`, accCtr(AccessRouteId.wolf_push), controller.werewolf.feiShu.test);
    
    // 【飞书机器人】获取配置状态
    router.get(`${API_VERSION}/werewolf/feiShu/status`, accCtr(AccessRouteId.wolf_push), controller.werewolf.feiShu.status);
    
    // 【飞书机器人】发送系统通知
    router.post(`${API_VERSION}/werewolf/feiShu/notify`, accCtr(AccessRouteId.wolf_push), controller.werewolf.feiShu.notify);
}

export default load;
