{
  "extends": "eslint-config-egg/typescript",
  "root": true,
  "rules": {
    // see https://github.com/eslint/eslint/issues/6274
    "generator-star-spacing": "off",
    "babel/generator-star-spacing": "off",
    "@typescript-eslint/no-useless-constructor": "off",
    "@typescript-eslint/indent": "off",
    "@typescript-eslint/semi": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "jsdoc/check-tag-names": "off",
    "jsdoc/require-param-description": "off",
    "jsdoc/require-param-name": "off",
    "spaced-comment": "off",
    "space-before-blocks": "off",
    "keyword-spacing": "off",
    "no-trailing-spaces": "off",
    "jsdoc/check-param-names": "off",
    "@typescript-eslint/no-var-requires": "off",
    "object-curly-spacing": "off",
    "quotes": "off",
    "array-bracket-spacing": "off",
    "prefer-const": "off",
    "eqeqeq": "off",
    "no-else-return": "off",
    "comma-dangle": "off",
    "comma-spacing": "off",
    "no-multi-spaces": "off",
    "space-before-function-paren": "off",
    "@typescript-eslint/no-array-constructor": "off",
    "no-extra-semi": "off",
    "dot-notation": "off",
    "key-spacing": "off",
    "comma-style": "off",
    "object-shorthand": "off",
    "eol-last": "off",
    "default-case": "off",
    "arrow-parens": "off",
    "node/prefer-global/console": "off",
    "no-multiple-empty-lines": "off",
    "arrow-spacing": "off",
    "space-infix-ops": "off",
    "semi-spacing": "off",
    "block-spacing": "off",
    "no-useless-concat": "off",
    "node/prefer-global/process": "off",
    "no-case-declarations": "off",
    "no-extra-boolean-cast": "off",
    "array-callback-return": "off",
    "brace-style": "off",
    "template-curly-spacing": "off",
    "jsdoc/require-returns-description": "off",
    "node/prefer-promises/fs": "off",
    "jsdoc/require-returns-type": "off",
    "linebreak-style": "off"
  },
}