import { IactivityAwardConf, IactivityAwardGroup, IactivityIndex, IgetAwardConfReq, IgetAwardGroupReq } from '../../model/wfActivityAward';
/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-09-21 13:49:58
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-16 16:45:46
 */

import BaseMegaController from './BaseMegaController';

export default class ActivityAwardController extends BaseMegaController {
    //1 查询全部运营活动 >43
    public async getActivityIndex() {
        const { ctx, logger } = this;
        try {

            const retList: IactivityIndex[] = await ctx.service.werewolf.activityAward.getActivityIndex();
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    //2 查询指定活动的组信息
    public async getActivityAwardGroup() {
        const { ctx, logger } = this;
        try {
            const req: IgetAwardGroupReq = ctx.request.body;
            const retList: IactivityAwardGroup[] = await ctx.service.werewolf.activityAward.getActivityAwardGroup(req);
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    //3 根据组查询奖励信息
    public async getActivityConf() {
        const { ctx, logger } = this;
        try {
            const req: IgetAwardConfReq = ctx.request.body;
            const retList: IactivityAwardConf[] = await ctx.service.werewolf.activityAward.getActivityConf(req);
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async insertAwardGroup() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.activityAward.insertAwardGroup(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async insertAward() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.activityAward.insertAward(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getSubItemList() {
        const { ctx } = this;
        try {
            // 验证入参
            ctx.validate({
                subItemIds: { type: 'string', required: true }
            });

            const { subItemIds } = ctx.request.body;
            const result = await ctx.service.werewolf.activityAward.getSubItemList(subItemIds);

            this.respSuccData(result)
        } catch (error) {
            ctx.logger.error('获取子道具列表失败', error);
            this.respFail(error.message)
        }
    }
}
