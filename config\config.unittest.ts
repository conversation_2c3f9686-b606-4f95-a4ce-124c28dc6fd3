/*
 * @Description: 本地单元测试配置
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercu<PERSON>
 * @Date: 2018-11-20 18:00:10
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-06-18 11:17:55
 */
// import { EggAppConfig, PowerPartial } from "egg";


export default () => {
  const config = {};
  const bizConfig = {
    // 修改主进程端口号
    cluster: {
      listen: {
        port: 7001
      }
    },

    mysql: {
      clients: {
        manager: {
          // 数据库名-账号数据库
          database: "mega_manager"
        },
        werewolf: {
          // 数据库名
          database: "werewolf"
        },
        werewolf_slave: {
          // 数据库名
          database: "werewolf",
          host: "coder.53site.com",
          // 端口号
          port: "3306",
          // 用户名
          user: "admin",
          // 密码
          password: "Mangosteen0!",
          //设置字符集
          charset: 'utf8mb4'
        },
        scriptkill: {
          database: "scriptkill"
        },
        chatgpt: {
          database: "chatgpt"
        }
        // aiim: {
        //   // 数据库名
        //   database: "aiim"
        // }
      },
      // 开发库数据库
      default: {
        host: "coder.53site.com",
        // 端口号
        port: "3306",
        // 用户名
        user: "admin",
        // 密码
        password: "Mangosteen0!",
        //设置字符集
        charset: 'utf8mb4'
      },
      // 是否加载到 app 上，默认开启
      app: true,
      // 是否加载到 agent 上，默认关闭
      agent: false
    },

    //redis
    redis: {
      clients: {
        liveDanmaku: {
          db: 0,
        },
        registRedis: {
          db: 1,
        },
        tokenRedis: {
          db: 3,
        },
        confRedis: {
          db: 8,
        },
        gameAreaRedis:{
          db: 8,
        },
        confBetaRedis: {
          db: 8,
          port: 6379,          // Redis port
          host: '**************',   // beta Redis host
          password: 'LA1954b!',
        }
      },
      default: {
        port: 6379,          // Redis port
        host: '**************',   // beta Redis host
        password: 'LA1954b!',
        db: 3,
      }
    },

    // clickHouse
    clickhouse:{
      clients: {
        werewolf_coder: {
          queryOptions: {
            database: "werewolf_coder",
          },
        },
      },
      default: {
        host: "************",
        port: "8123",
        user: "default",
        password: "Mangosteen0!"
      }
    },

    //mongo
    mongo: {
      client: {
        host: '**************',
        port: 27017,
        name: 'werewolf',
        user: 'admin',                                                                                                                                                                                                                                                                                                                                                                                                                                                    
        password: 'LA1954b!',
        options: {},
      }
    },

    //图片域名
    imgDomain: 'coder.53site.com',
    //接口域名
    phpDomain: 'coder.53site.com',
    //php活动接口父地址
    phpAtyBaseUrl: 'http://coder.53site.com/Werewolf/DailyMission/PHPCode/api',
    //php推送到android
    phpPushAndoird: 'http://coder.53site.com/Werewolf/Apns/ApnsAPI_batch_push_android_v3.php',
    //php推送到Ios
    phpPushIos: 'http://coder.53site.com/Werewolf/Apns/ApnsAPI_batch_push_ios_v3.php',
    //下载headicon
    headIconDownUrl: "http://coder.53site.com/Werewolf/OSS/downloadheadicon.php",

    goActivity: "http://localhost:8284/api/v1/activity",

    serverEnv: "local",

    //狼王爬虫频率
    loopKingInterval: "10s",//1分钟
    //2w框定时检测
    loopCoin2wInterval: '15s',//10s间隔
    //世界广播定时检测
    broadcastInterval: '10s',//10s间隔
    //新手辅助机器人定时检测
    newPlayerRobotInterval: '20s',//20s间隔
    //世界广播定时检测
    broadcastRun: 0,  //1开启 0关闭
    //新手辅助机器人定时检测
    newPlayerRobotRun: 1,  //1开启 0关闭
    //php充值2w钻
    php2wTransacUrl: "http://coder.53site.com/Werewolf/DailyMission/PHPCode/api/20000/transaction.php",
    //exchange 系统
    WerewolfJPExchange: 'http://*************:9919/',
    //exchange 灰度系统
    WerewolfExchange_BETA: 'http://*************:9919/',
    //funroom 系统
    WerewolfJPFunRoom: 'http://**************:9926/',
    //funroom 系统
    WerewolfJPFunRoom_BETA: 'http://**************:9926/',
    //爬虫路由
    crawelHost: "http://localhost:7011/crawler/page",
    //推送服务健康监测
    pushHealthCheckUrl: "http://*************:4632/wfPush/test",

    // 天狼飞书bot 
    WerewolfFeiShuBot: 'https://open.feishu.cn/open-apis/bot/v2/hook/a9afdba4-e1d6-4fce-aaed-91d21b225d7f',
  };
  return {
    ...config,
    ...bizConfig
  };
};
