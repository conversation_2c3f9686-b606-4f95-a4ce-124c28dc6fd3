// 飞书机器人消息发送服务测试 
import * as assert from 'assert'; 
import { Context } from 'egg'; 
import { app } from 'egg-mock/bootstrap'; 
 
describe('test/app/service/werewolf/feiShu.test.ts', () => {
    let ctx: Context;

    before(async () => {
        ctx = app.mockContext();
    });

    describe('sendMessage()', () => {
        it('should send message successfully', async () => {
            // Mock curl response
            app.mockHttpclient(app.config.WerewolfFeiShuBot, {
                data: {
                    code: 0,
                    msg: 'success'
                },
                status: 200
            });

            const title = '测试标题';
            const context = '测试内容';

            const result = await ctx.service.werewolf.feiShu.sendMessage(title, context);

            assert.deepStrictEqual(result, {
                success: true,
                message: '消息发送成功'
            });
        });

        it('should handle missing webhook config', async () => {
            // Store original config
            const originalConfig = app.config.WerewolfFeiShuBot;
            // Set webhook config to undefined
            app.config.WerewolfFeiShuBot = undefined;

            const title = '测试标题';
            const context = '测试内容';

            const result = await ctx.service.werewolf.feiShu.sendMessage(title, context);

            assert.deepStrictEqual(result, {
                success: false,
                message: '飞书配置不存在'
            });

            // Restore original config
            app.config.WerewolfFeiShuBot = originalConfig;
        });

        it('should handle failed response', async () => {
            // Mock failed response
            app.mockHttpclient(app.config.WerewolfFeiShuBot, {
                data: {
                    code: 1,
                    msg: '发送失败'
                },
                status: 200
            });

            const title = '测试标题';
            const context = '测试内容';

            const result = await ctx.service.werewolf.feiShu.sendMessage(title, context);

            assert.deepStrictEqual(result, {
                success: false,
                message: '消息发送失败: 发送失败'
            });
        });

        it('should handle network error', async () => {
            // Mock network error
            app.mockHttpclient(app.config.WerewolfFeiShuBot, () => {
                throw new Error('网络错误');
            });

            const title = '测试标题';
            const context = '测试内容';

            const result = await ctx.service.werewolf.feiShu.sendMessage(title, context);

            assert.deepStrictEqual(result, {
                success: false,
                message: '发送异常: 网络错误'
            });
        });
    });
});

