// 飞书机器人消息发送服务测试
import * as assert from 'assert';
import { Context } from 'egg';
import { app } from 'egg-mock/bootstrap';

describe('test/app/service/werewolf/feiShu.test.ts', () => {
    let ctx: Context;

    before(async () => {
        await app.ready();
        ctx = app.mockContext();
    });

    describe('sendMessage()', () => {
        it('should check service exists', async () => {
            console.log('ctx.service:', Object.keys(ctx.service));
            console.log('ctx.service.werewolf:', ctx.service.werewolf ? Object.keys(ctx.service.werewolf) : 'undefined');
            console.log('ctx.service.werewolf.feiShu:', ctx.service.werewolf?.feiShu);

            assert(ctx.service.werewolf, 'werewolf service should exist');
            assert(ctx.service.werewolf.feiShu, 'feiShu service should exist');
        });

        it('should send message successfully', async () => {
            // Mock curl response
            app.mockHttpclient('https://open.feishu.cn/open-apis/bot/v2/hook/a9afdba4-e1d6-4fce-aaed-91d21b225d7f', {
                data: {
                    code: 0,
                    msg: 'success'
                },
                status: 200
            });

            // Set test webhook config
            app.config.WerewolfFeiShuBot = 'https://open.feishu.cn/open-apis/bot/v2/hook/a9afdba4-e1d6-4fce-aaed-91d21b225d7f';

            const title = '测试标题';
            const context = '测试内容';

            const result = await ctx.service.werewolf.feiShu.sendMessage(title, context);

            assert.deepStrictEqual(result, {
                success: true,
                message: '消息发送成功'
            });
        });

    });
});

