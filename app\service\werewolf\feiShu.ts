/*
 * @Description: 飞书机器人消息发送服务
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: AI Assistant
 * @Date: 2023-11-22 10:00:00
 * @LastEditors: AI Assistant
 * @LastEditTime: 2023-11-22 11:00:00
 */

import { Service } from 'egg';
import * as crypto from 'crypto';

/**
 * 飞书消息结构
 */
interface FeishuMessage {
    timestamp?: number;
    sign?: string;
    msg_type: string;
    card: {
        header: {
            title: {
                tag: string;
                content: string;
            };
            template?: string;
        };
        elements: Array<{
            tag: string;
            text: {
                tag: string;
                content: string;
            };
        }>;
    };
}

export default class FeiShuService extends Service {

    /**
     * 发送飞书机器人消息
     * @param title 消息标题
     * @param context 消息内容
     * @returns 发送结果
     */
    public async sendMessage(title: string, context: string) {
        const { app, ctx } = this;
        const { feishuConfig } = app.config;

        const webhook = app.config.WerewolfFeiShuBot;
        if (!webhook) {
            ctx.logger.error('FeiShuService:sendMessage - 飞书配置不存在');
            return { success: false, message: '飞书配置不存在' };
        }
        console.log('test webhook:', webhook);
        try {
            // 构造飞书消息结构，参考Java实现
            const feishuMessage: FeishuMessage = {
                msg_type: 'interactive',
                card: {
                    header: {
                        title: {
                            tag: 'plain_text',
                            content: title,
                        },
                        template: 'blue',
                    },
                    elements: [],
                },
            };

            // 创建元素
            const element = {
                tag: 'div',
                text: {
                    tag: 'lark_md',
                    content: context,
                },
            };

            // 添加元素到消息中
            feishuMessage.card.elements.push(element);
            console.log('test feishuMessage:', feishuMessage);

            // 使用Egg.js的curl方法发送请求
            const result = await ctx.curl(webhook, {
                method: 'POST',
                contentType: 'json',
                data: feishuMessage,
                dataType: 'json',
            });
            console.log('test result:', result);
            ctx.logger.info(`飞书机器人发送消息result: ${JSON.stringify(result.data)}`);

            if (result.status === 200 && result.data && result.data.code === 0) {
                return { success: true, message: '消息发送成功' };
            } else {
                ctx.logger.error(`FeiShuService:sendMessage - 消息发送失败: ${JSON.stringify(result.data)}`);
                return { success: false, message: `消息发送失败: ${result.data?.msg || '未知错误'}` };
            }
        } catch (error) {
            ctx.logger.error(`FeiShuService:sendMessage - 异常: ${error.message}`);
            return { success: false, message: `发送异常: ${error.message}` };
        }
    }
}