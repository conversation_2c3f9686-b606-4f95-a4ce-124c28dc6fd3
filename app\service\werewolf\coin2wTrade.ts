/*
 * @Description: 2w框服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2020-08-31 13:17:33
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-15 15:39:08
 */

import BaseMegaService from './BaseMegaService';
import { Icoin2wListReq, Icoin2wItem, Icoin2wUpdateInfoReq, Coin2wStatus, Icoin2wUploadNoteSuccReq, IsendDingReq } from '../../model/wf2wCoin';
import moment = require('moment');
import { IavatarFramePeriodv2 } from '../../model/werewolf2';
import { IobtianItemReq } from '../../model/wfExchange';
export default class Coin2wTradeService extends BaseMegaService {

    public async getTwoWCoinUserList(req: any): Promise<any> {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `SELECT a.user_id AS id,b.nickname AS \`name\`
            FROM coin_2w AS a
            LEFT JOIN tuser  AS b ON b.no = a.user_id
            LEFT JOIN base20000_avatar_frame_period_v2 as c ON c.id = a.avatar_frame_period_id
            WHERE  b.delsign = 0
            GROUP BY a.user_id
            ORDER BY a.create_time DESC`;


            const array: any[] = await this.selectList(sqlStr)
            return array
        }
        catch (error) {
            logger.error(error)
            throw error;
        }
    }

    /**
     * 获得2w框订单列表
     * @param req 
     */
    public async getTwoWCoinList(req: Icoin2wListReq): Promise<Icoin2wItem[]> {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = "";
            //all
            if (req.status == -1) {
                sqlStr = `SELECT a.*,b.nickname,
                IFNULL(c.name,'')as period_name,
                '' as author 
                FROM coin_2w AS a
                LEFT JOIN tuser  AS b ON b.no = a.user_id
                LEFT JOIN item_dic as c ON c.item_id = a.select_avatar_frame_id
                WHERE  b.delsign = 0
                ${req.userId != null ? `and a.user_id = ${req.userId}`: ""}
                and c.item_cate_id IN (2010,2020)
                ORDER BY a.create_time DESC`;
            } else {
                sqlStr = `SELECT a.*,b.nickname,
                IFNULL(c.name,'')as period_name,
                '' as author 
                FROM coin_2w AS a
                LEFT JOIN tuser  AS b ON b.no = a.user_id
                LEFT JOIN item_dic as c ON c.item_id = a.select_avatar_frame_id
                WHERE  b.delsign = 0
                AND a.status = ${req.status}
                ${req.userId != null ? `and a.user_id = ${req.userId}`: ""}
                and c.item_cate_id IN (2010,2020)
                ORDER BY a.create_time DESC `;
            }
            const array: Icoin2wItem[] = await this.selectList(sqlStr)
            return array
        }
        catch (error) {
            logger.error(error)
            throw error;
        }
    }

    /**
     * 更新2w框订单信息
     * @param req 
     */
    public async updateCoin2winfo(req: Icoin2wUpdateInfoReq) {
        const { app, ctx, logger } = this;
        try {

            // var avatar_frame_period = req.avatar_frame_period_id+'';
            
            // "ID: 8267 名称: 画板5碎片8"
            // req.avatar_frame_period_id = avatar_frame_period.substring(avatar_frame_period.indexOf("名称: ") + 4, avatar_frame_period.length-1);


            //无资源
            if (req.status == Coin2wStatus.COIN_2W_NULL) {
                await this.execSql(`UPDATE coin_2w SET status = ? WHERE id = ?  `, [req.status, req.id]);
                return;
            }
            //旧订单信息
            const oldTradeInfo = await this.selectOne(`SELECT * FROM coin_2w WHERE id = ?`, [req.id])
            if (!oldTradeInfo) {
                throw new Error("数据错误")
            }
            //新头像框信息
            const newAvatarInfo = await this.selectOne(`SELECT *  FROM item_dic WHERE item_id = ? AND item_cate_id IN (2010,2020);`, [req.avatar_frame_period_id])
            newAvatarInfo.avatar_frame_id = newAvatarInfo.item_id;
            if (!newAvatarInfo || !newAvatarInfo.avatar_frame_id) {
                throw new Error("数据错误")
            }
            //新的头像框
            let select_avatar_frame_id: number = newAvatarInfo.avatar_frame_id;
            //1 旧订单存在期数
            if (oldTradeInfo.avatar_frame_period_id) {
                //1.1 旧订单期数与新订单期数相等，直接更新
                if (oldTradeInfo.avatar_frame_period_id == req.avatar_frame_period_id) {
                    await this.updateCoin2winfoImp(req, select_avatar_frame_id)
                } else {
                    //1.2 不相等
                    //1.2.1 减少旧框已售卖
                    await this.decreaseAvatarPeriodSellNum(oldTradeInfo.avatar_frame_period_id)
                    //1.2.2 增加新框已售卖和显示售卖
                    await this.increaseAvatarPeriodSellNum(req.avatar_frame_period_id)
                    //1.2.3 更新订单信息
                    await this.updateCoin2winfoImp(req, select_avatar_frame_id)
                    //1.2.4 检测是否触发上新
                    if (newAvatarInfo.selled_show_num + 1 >= newAvatarInfo.sell_num) {
                        //上新
                        await this.putDownOldAndPutOnNew(req.avatar_frame_period_id)
                    }
                }
            } else {
                //2 旧订单不存在期数
                //2.1 增加新框已售卖和显示售卖
                await this.increaseAvatarPeriodSellNum(req.avatar_frame_period_id)
                //2.2 更新订单信息
                await this.updateCoin2winfoImp(req, select_avatar_frame_id)
                //2.2 检测是否触发上新
                if (newAvatarInfo.selled_show_num + 1 >= newAvatarInfo.sell_num) {
                    //上新
                    await this.putDownOldAndPutOnNew(req.avatar_frame_period_id)
                }
            }
        } catch (error) {
            logger.error(error)
            throw error;
        }
    }

    public async updateCoin2winfoImp(req: Icoin2wUpdateInfoReq, select_avatar_frame_id: number) {
        //更新订单信息
        await this.execSql(`
UPDATE coin_2w SET status = ?,to_user_id = ?,avatar_frame_period_id = ? ,select_avatar_frame_id = ?,note_command= ? WHERE id =  ?;
`, [req.status,
        req.to_user_id,
        req.avatar_frame_period_id,
            select_avatar_frame_id,
        req.note_command,
        req.id
        ])
    }

    //增加售卖数量
    public async increaseAvatarPeriodSellNum(periodId: number) {
        await this.execSql(`UPDATE base20000_avatar_frame_period_v2  SET selled_num  = selled_num + 1 ,selled_show_num  = selled_show_num+1 WHERE  id  = ? `, [periodId])
    }

    public async decreaseAvatarPeriodSellNum(periodId: number) {
        await this.execSql(`UPDATE base20000_avatar_frame_period_v2  SET selled_num  = selled_num - 1  WHERE  id  = ? `, [periodId])
    }

    /**
     * 更新订单刻字信息
     * @param req 
     */
    public async uploadCoin2wNoteSuccess(req: Icoin2wUploadNoteSuccReq) {
        const { app, ctx, logger } = this;
        try {
            await this.execSql(`UPDATE coin_2w SET note_id = ? WHERE id = ?;`, [req.note_id, req.id]);
        } catch (error) {
            logger.error(error)
            throw error;
        }
    }

    /**
     * 发送订单全部信息
     * @param req 
     */
    public async sendAllRes(req: Icoin2wItem) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get('manager')
        const managerConn = await manager.beginTransaction()

        try {
            //1 查询头像框item_id
            const avatarFrameResult = await this.selectOne(`SELECT id FROM item_dic WHERE item_id = ? AND item_cate_id in (2010,2020);`, [req.select_avatar_frame_id]);
            if (!avatarFrameResult || !avatarFrameResult.id) {
                throw new Error("头像框不存在")
            }
            //1.2通过exchange发送头像框
            const addReq = ctx.service.werewolf.exchangeRpc.geneAddReqByItemList(req.user_id, [
                { itemDicId: avatarFrameResult.id, num: 1 },
            ])
            await ctx.service.werewolf.exchangeRpc.itemObtain(addReq)

            //2 插入成就
            await this.execSql(`INSERT IGNORE INTO user_achievement (user_id,achievement_id,complete_num,level,delsign)
            VALUES(?,?,1,0,0);`, [req.to_user_id, req.achievement_id])
            

            //3 刻字
        //     await this.execSql(`INSERT IGNORE INTO user_avatarframe (user_id,avatarframe_id,note_id,delsign)
        //   VALUES(?,?,?,0);`, [req.to_user_id, req.select_avatar_frame_id, req.note_id])
            await this.execSql(`UPDATE user_avatarframe SET  note_id = ? WHERE user_id = ? AND avatarframe_id =  ?;`,[req.note_id,req.to_user_id,req.select_avatar_frame_id])

            // 4 状态
            await this.execSql(`UPDATE coin_2w SET status=2,send_time=NOW()  WHERE id = ?;`, [req.id]);

            let avatarFrameUrl = this.getAvatarFrameImg(req.select_avatar_frame_id)
            let noteUrl = this.getNoteImg(req.note_id)
            let achievementUrl = this.getAchievementImg(req.achievement_id)
            const msg = `* UserId:${req.to_user_id}资源全部发放成功！\n`
                + ` * 2W头像框【${req.period_name}】\n`
                + `  ![头像框](${avatarFrameUrl}) \n`
                + ` * 刻字【${req.note_command}】\n`
                + `  ![刻字](${noteUrl}) \n`
                + ` * 成就`
                + `  ![成就](${achievementUrl}) \n`
                ;
            //4 钉钉
            const dingReq: IsendDingReq = {
                msgtype: 'markdown',
                markdown: {
                    title: "发放2W头像框全部资源通知",
                    text: msg,
                }
            }
            logger.info("待发送钉钉信息", dingReq);
            await this.sendDingtalk(dingReq);

             //5 飞书(2w第二版)
             let aperiodName = req.period_name;
             let noteCommand = req.note_command;

             let feiShuMsg = `* UserId:${req.to_user_id}资源全部发放成功！\n`

             const feiReq = {
                 text:feiShuMsg,

                 period_name: aperiodName,
                 avatarFrame: avatarFrameUrl,

                 note_command: noteCommand,
                 note: noteUrl,

                 achievement: achievementUrl

             }

             await this.sendFeishuTalk(feiReq);

            await managerConn.insert('wf_admin_avatar_frame', {
                admin_id: req.uid,
                user_id: req.to_user_id,
                avatar_frame_id: req.select_avatar_frame_id,
                avatar_frame_name: req.period_name,
                type: 2,
            })
            await managerConn.commit()
        } catch (error) {
            logger.error(error)
            await managerConn.rollback()
            throw error;
        }
    }

    public getAvatarFrameImg(avatarFrameId) {
        if (this.globalEnv() == 'prod') {
            return `http://img.53site.com/Werewolf/Frame/${avatarFrameId}_player.png`;
        }
        return `http://coder.53site.com/Werewolf/Frame/${avatarFrameId}_player.png`;
    }

    /**
     * 获得刻字图片地址
     * @param {*} noteId
     */
    public getNoteImg(noteId) {
        if (this.globalEnv() == 'prod') {
            return `http://img.53site.com/Werewolf/frame_note/${noteId}.png`;
        }
        return `http://coder.53site.com/Werewolf/frame_note/${noteId}.png`;
    }

    /**
     * 获得成就图片地址
     * @param {*} ahievementId
     */
    public getAchievementImg(ahievementId) {
        if (this.globalEnv() == 'prod') {
            return `http://img.53site.com/Werewolf/achieveNew/achieve_${ahievementId}.png`;
        }
        return `http://coder.53site.com/Werewolf/achieveNew/achieve_${ahievementId}.png`;
    }

    public globalEnv() {
        const { app, ctx, logger } = this;
        this.logger.info("当前运行环境", app.config.serverEnv);
        if (app.config.serverEnv == 'prod') {
            return 'prod';
        } else {
            return 'development';
        }
    }

    /**
     * 定时检测2w框上架
     */
    public async checkPeriodPutOn() {
        const { app, ctx, logger } = this;
        logger.info("检测2w框上架情况");
        try {
            //todo 1 首先检测上架商品数
            const { on_total } = await this.selectOne(`SELECT COUNT(*) as on_total FROM base20000_avatar_frame_period_v2
             WHERE period_status = 1 AND delsign = 0;`);
            logger.info("上架中数量", on_total);
            //2查询等待中队列
            const { ready_total } = await this.selectOne(`SELECT COUNT(*) as ready_total FROM base20000_avatar_frame_period_v2
              WHERE period_status = 0 AND delsign = 0;`);
            logger.info("上架队列数量", ready_total);
            if (ready_total < 3) {
                await this.sendDIngtalkNoInverity(ready_total)
            }
            //1上架不足3，自动延续
            if (on_total < 3) {
                let need_total = 3 - on_total;
                if (need_total > ready_total) {
                    need_total = ready_total;
                }
                if (ready_total == 0) {
                    return;
                }
                logger.info("需要上架数量", need_total);
                const { idsStr } = await this.selectOne(`SELECT GROUP_CONCAT(id) as idsStr 
                            FROM (SELECT id  FROM base20000_avatar_frame_period_v2 WHERE period_status = 0 ORDER BY puton_sort ASC,id ASC LIMIT ?) as a;`,
                        [need_total])
                logger.info("需要上架id类列表表", idsStr);        
                //上架列表
                const db = this.app.mysql.get('werewolf');
                const tx = await db.beginTransaction();
                try{
                        //上架
                        await tx.query(`UPDATE base20000_avatar_frame_period_v2 SET period_status = 1,
                        real_starttime=NOW(),endtime=date_add(now(), interval 14 day) 
                        WHERE id IN (${idsStr})`, []);
                        await tx.commit()
                }catch (error){
                    await tx.rollback();
                    logger.error(error)
                }
            } else {
                //上架已经满足，检测是否应该下架
                await this.checkPeriodPutDown()
            }
        } catch (err) {
            logger.error("检测上架错误", err);
        }
    }

    /**
     * 检测是否有下架
     */
    public async checkPeriodPutDown() {
        const { app, ctx, logger } = this;
        logger.debug("检测2w框下架情况");
        try {
            //todo 1查出一个到期的
            const result = await this.selectOne(`SELECT id FROM base20000_avatar_frame_period_v2 WHERE period_status = 1 
            AND delsign = 0 AND endtime < NOW() LIMIT 1`)
            if (!result || !result.id) {
                logger.info("没有需要下架的");
                return;
            }
            const oldId = result.id
            await this.putDownOldAndPutOnNew(oldId)
        } catch (err) {
            logger.error("检测上架错误", err);
        }
    }

    /**
     * 下架旧的期数并上新
     * @param oldId 
     */
    public async putDownOldAndPutOnNew(oldId: number) {
        const { app, ctx, logger } = this;
        logger.info("下架period id", oldId);
        const db = this.app.mysql.get('werewolf');
        const tx = await db.beginTransaction();
        try{
            //todo 2 下架
            await tx.query(`UPDATE base20000_avatar_frame_period_v2 SET period_status = 2,real_endtime = NOW() WHERE id = ?`, [oldId]);
            //todo 3 上一个新的
            const newResult = await tx.query(`SELECT id FROM base20000_avatar_frame_period_v2 WHERE period_status = 0 AND delsign = 0 
            ORDER BY puton_sort ASC,id ASC LIMIT 1`)
            
            if (!newResult || !newResult[0]) {
                logger.error("查询上一个失败newResult",newResult)
                await tx.rollback();
                await this.sendDIngtalkNoInverity(0)
                return;
            }
            //4 上新
            logger.info("准备上架新的id", newResult[0].id);
            await tx.query(`UPDATE base20000_avatar_frame_period_v2 SET period_status = 1 ,
            real_starttime = NOW(),endtime = date_add(now(), interval 14 day)
            WHERE id = ?`, [newResult[0].id]);
            await tx.commit();
        }catch (error) {
            tx.rollback();
            logger.error(error)
        }
       
    }

    public async sendDIngtalkNoInverity(leftNum: number) {
        let hour = moment().get("hours");
        if (hour > 18 || hour < 9) {
            return;
        }
        let day = moment().format("YYYY-MM-DD")

        let sendNums = await this.app.redis.get("tokenRedis").hget("consol_no_inve_ding", day)
        if (!sendNums) {
            sendNums = 0
        }
        sendNums = parseInt(sendNums, 10)
        if (sendNums > 5) {
            return;
        } else {
            sendNums++
            await this.app.redis.get("tokenRedis").hset("consol_no_inve_ding", day, sendNums)
        }
        const dingReq: IsendDingReq = {
            msgtype: 'markdown',
            markdown: {
                title: "2W头像框库存报警",
                text: `### 2W头像框等待队列剩余${leftNum},请及时补充`,
            }
        }
        // await this.sendDingtalk(dingReq);
        // //飞书
        // await this.sendFeishuTextTalk(dingReq.markdown.title,dingReq.markdown.text);

    }

    public async sendDingtalk(dingReq: IsendDingReq) {
        const { app, ctx, logger } = this;
        let host = app.config.phpAtyBaseUrl;
        //正式上线时屏蔽掉
        if (app.config.serverEnv == 'local' || app.config.serverEnv == 'coder') {
            host = "http://coder.53site.com/Werewolf/DailyMission/PHPCode/api";
        } else {
            host = "http://werewolf.53site.com/Werewolf/DailyMission/PHPCodeProd/api";
        }
        const curlResp = await ctx.curl(host + '/Dingtalk/send.php', {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            data: dingReq,
            dataType: 'json',
            timeout: 30000, // 30 秒超时
        });

        const { status, headers, data } = curlResp
        if (status == 200) {
            this.logger.debug("发送php成功", data);
        } else {
            this.logger.error("发送php失败", status, data);
        }
    }

    public async sendFeishuTalk(feiReq: any) {
        const { app, ctx, logger } = this;
        let host = app.config.phpAtyBaseUrl;



        let feishuReq = {
            msg_type: 'post',
            content: {
                post: {
                    zh_cn: {
                        title: feiReq.text,
                        content: [
                            [
                                {
                                    tag: "a",
                                    text: "2w头像框："+feiReq.period_name+"\n",
                                    href: feiReq.avatarFrame
                                },
                                {
                                    tag: "a",
                                    text: "刻字："+feiReq.note_command+"\n",
                                    href: feiReq.note
                                },
                                {
                                    tag: "a",
                                    text: "成就\n",
                                    href: feiReq.achievement
                                }
                            ]
                        ]
                    }
                }
            }
        };

        //正式上线时屏蔽掉
        if (app.config.serverEnv == 'local' || app.config.serverEnv == 'coder') {
            host = "http://coder.53site.com/Werewolf/DailyMission/PHPCode/api";
        } else {
            host = "http://werewolf.53site.com/Werewolf/DailyMission/PHPCodeProd/api";
        }
        const curlResp = await ctx.curl(host + '/Dingtalk/sendFeishu.php', {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            data: feishuReq,
            dataType: 'json',
            timeout: 30000, // 30 秒超时
        });

        const { status, headers, data } = curlResp
        if (status == 200) {
            this.logger.debug("发送php成功", data);
        } else {
            this.logger.error("发送php失败", status, data);
        }
    }

    public async sendFeishuTextTalk(titleReq: any,textReq:any) {
        const { app, ctx, logger } = this;
        let host = app.config.phpAtyBaseUrl;

        let feishuReq = {
            msg_type: "post",
            content: {
                post: {
                    zh_cn: {
                        title: titleReq,
                        content: [
                            [{
                                    tag: "text",
                                    text: textReq
                                }
                            ]
                        ]
                    }
                }
            }
        };

        //正式上线时屏蔽掉
        if (app.config.serverEnv == 'local' || app.config.serverEnv == 'coder') {
            host = "http://coder.53site.com/Werewolf/DailyMission/PHPCode/api";
        } else {
            host = "http://werewolf.53site.com/Werewolf/DailyMission/PHPCodeProd/api";
        }
        const curlResp = await ctx.curl(host + '/Dingtalk/sendFeishu.php', {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            data: feishuReq,
            dataType: 'json',
            timeout: 30000, // 30 秒超时
        });

        const { status, headers, data } = curlResp
        if (status == 200) {
            this.logger.debug("发送php成功", data);
        } else {
            this.logger.error("发送php失败", status, data);
        }
    }

    /**
     * 主动下架
     * @param req 
     */
    public async putDown(req: IavatarFramePeriodv2) {
        const { app, ctx, logger } = this;
        // logger.debug("主动下架", req);
        //todo 2 下架
        await this.execSql(`UPDATE base20000_avatar_frame_period_v2 SET period_status = 2,real_endtime = NOW() WHERE id = ?`,
            [req.id]);
        //todo 3 上一个新的
        const newResult = await this.selectOne(`SELECT id FROM base20000_avatar_frame_period_v2 WHERE period_status = 0 AND delsign = 0 ORDER BY puton_sort ASC,id ASC LIMIT 1
         `)
        logger.info("准备上架新的id", newResult.id);
        if (!newResult || !newResult.id) {
            await this.sendDIngtalkNoInverity(0)
            return;
        }
        //4 上新
        await this.execSql(`UPDATE base20000_avatar_frame_period_v2 SET period_status = 1,
        real_starttime = NOW(),endtime=date_add(now(), interval 14 day)
         WHERE id = ?`, [newResult.id]);
    }
}