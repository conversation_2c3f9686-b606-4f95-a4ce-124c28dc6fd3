{"name": "console-server-cn", "version": "1.12.0", "description": "", "private": true, "egg": {"typescript": true}, "scripts": {"prod2": "egg-scripts start --workers=1  --port=7002 --daemon --title=egg-server-ConsoleSystemServer2", "stop2": "egg-scripts stop --title=egg-server-ConsoleSystemServer2", "prod3": "egg-scripts start --workers=1  --port=7003 --daemon --title=egg-server-ConsoleSystemServer3", "stop3": "egg-scripts stop --title=egg-server-ConsoleSystemServer3", "stop": "egg-scripts stop --title=egg-server-ConsoleSystemServer", "start": "egg-scripts start --workers=1 --daemon --title=egg-server-ConsoleSystemServer", "beta": "EGG_SERVER_ENV=mbeta NODE_ENV=production npm run start", "coder": "EGG_SERVER_ENV=coder NODE_ENV=production npm run start", "prodDocker": "NODE_ENV=production egg-scripts start", "betaDocker": "EGG_SERVER_ENV=mbeta NODE_ENV=production egg-scripts start", "coderDocker": "EGG_SERVER_ENV=coder NODE_ENV=production egg-scripts start", "localDocker": "EGG_SERVER_ENV=local NODE_ENV=production egg-scripts start", "kill": "egg-scripts stop", "dev": "egg-bin dev -r egg-ts-helper/register", "debug": "egg-bin debug -r egg-ts-helper/register", "test-local": "egg-bin test -r egg-ts-helper/register", "test-single": "egg-bin test -r egg-ts-helper/register --", "test": "npm run lint -- --fix && npm run test-local", "cov": "egg-bin cov -r egg-ts-helper/register", "tsc": "tsc -p tsconfig.json", "ci": "npm run lint  && npm run tsc", "autod": "autod", "lint": "eslint . --ext .ts", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js", "clean": "tsc -b --clean"}, "dependencies": {"@apla/clickhouse": "^1.6.4", "ali-oss": "^6.18.1", "cheerio": "^1.0.0-rc.2", "egg": "^3.27.1", "egg-jwt": "^2.2.1", "egg-mongo-native": "^3.5.0", "egg-mysql": "^3.4.0", "egg-oss": "^2.0.0", "egg-redis": "^2.5.0", "egg-scripts": "^2.17.0", "egg-static": "^2.3.1", "egg-validate": "^2.0.2", "lint-staged": "^10.5.4", "md5": "^2.3.0", "moment": "^2.29.4", "node-rsa": "^1.1.1", "pyfl": "^1.1.4", "tslib": "^1.14.1"}, "devDependencies": {"@eggjs/tsconfig": "^1.1.0", "@types/cheerio": "^0.22.1", "@types/mocha": "^10.0.1", "egg-bin": "^5.9.0", "egg-mock": "^5.5.0", "eslint": "^8.31.0", "eslint-config-egg": "^12.1.0", "typescript": "^4.9.4", "@types/node": "^7.10.14", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "autod": "^2.10.1", "autod-egg": "^1.1.0", "egg-ci": "^1.19.1", "egg-ts-helper": "^1.35.1", "eslint-plugin-import": "^2.28.1", "husky": "^4.3.8", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0"}, "engines": {"node": ">=8.9.0"}, "ci": {"version": "8"}, "repository": {"type": "git", "url": ""}, "eslintIgnore": ["coverage"], "lint-staged": {"**/*.{js,jsx,less}": ["prettier --write", "git add"], "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.less": "stylelint --syntax less"}, "author": "", "license": "MIT"}