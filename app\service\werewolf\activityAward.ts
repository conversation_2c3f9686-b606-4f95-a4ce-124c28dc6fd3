/*
 * @Description: 运营活动奖励
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-09-17 17:29:48
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-16 17:51:27
 */

import BaseMegaService from './BaseMegaService';
import { IactivityAwardConf, IactivityAwardGroup, IactivityIndex, IgetAwardConfReq, IgetAwardGroupReq } from '../../model/wfActivityAward';

export default class ActivityAwardService extends BaseMegaService {

   //1 查询全部运营活动 >43
   public async getActivityIndex(): Promise<IactivityIndex[]> {
      const { logger } = this;
      try {
         const sqlStr = `SELECT
	id,
	name,
	start_time,
	end_time,
	end_data_time,
	sql_admin
FROM
	activity_index
WHERE
	id > 230 OR id in (128)
AND is_show = 1
ORDER BY
	id DESC; `
         // tslint:disable-next-line:one-variable-per-declaration
         let ret: IactivityIndex[] = await this.selectList(sqlStr, [])
         return ret;
      } catch (err) {
         logger.error(err);
         throw err;
      }
   }

   //2 查询指定活动的组信息
   public async getActivityAwardGroup(req: IgetAwardGroupReq): Promise<IactivityAwardGroup[]> {
      const { logger } = this;
      try {
         const sqlStr = ` SELECT * FROM activity_comm_award_group WHERE activity_id = ? ORDER BY group_index DESC `;
         const ret: IactivityAwardGroup[] = await this.selectList(sqlStr, [req.activity_id])
         if(ret == null){
            return [];
         }
         return ret;
      } catch (err) {
         logger.error(err);
         throw err;
      }
   }

   //3 根据组查询奖励信息
   public async getActivityConf(req: any): Promise<IactivityAwardConf[]> {
      const { logger } = this;
      try {
         const sqlStr = `SELECT a.*,b.name as item_name,b.item_id,b.item_cate_id,c.name as item_cate_name
         ,c.item_table
         ,d.img_name
         ,e.price
			,e.coin_id
         ,f.name as price_name
			,f.img_name as coin_img_name
         FROM activity_comm_award_conf  as a
         LEFT JOIN item_dic as b on b.id = a.item_dic_id
         LEFT JOIN item_cate as c on c.id = b.item_cate_id
         LEFT JOIN normal_item as d on b.item_id = d.no
         LEFT JOIN activity_comm_price_conf as e on e.activity_id = a.activity_id AND e.award_id = a.award_id
			LEFT JOIN coin as f on f.id = e.coin_id
         WHERE a.activity_id = ?
         AND a.award_group_id = ?;`;
         const ret: IactivityAwardConf[] = await this.selectList(sqlStr, [req.activity_id, req.award_group_id])
         return ret;
      } catch (err) {
         logger.error(err);
         throw err;
      }
   }

   public async insertAwardGroup(req: any) {
      const { app } = this;
      const db = app.mysql.get('werewolf');
      const conn = await db.beginTransaction();
      try {
         let groupIndex: number;
         if (req.group_index) {
            groupIndex = req.group_index;
         } else {
            const sqlSelectMax = ` SELECT (IFNULL(MAX(group_index),0) + 100) AS group_index_new FROM activity_comm_award_group WHERE activity_id = ? `;
            const maxResult: any = await conn.query(sqlSelectMax, [req.activity_id]);
            groupIndex = maxResult[0].group_index_new;
         }
         const sqlStr = ` INSERT INTO activity_comm_award_group (activity_id, group_name, group_index, edit_enable )
         VALUES (?,?,?,1) `;
         const insertResult = await conn.query(sqlStr, [
            req.activity_id,
            req.group_name,
            groupIndex,
         ]);
         const groupId = insertResult.insertId;
         this.logger.info(`Inserted award group with ID: ${groupId}`);
         // 更新奖励配置表的award_group_id
         const updateSql = `UPDATE activity_comm_award_conf
            SET award_group_id = ?
            WHERE activity_id = ?
            AND award_id BETWEEN ? AND ?`;
         await conn.query(updateSql, [
            groupId,
            req.activity_id,
            groupIndex,
            groupIndex + 100
         ]);


         await conn.commit(); // 提交事务

      } catch (err) {
         await conn.rollback(); // 一定记得捕获异常后回滚事务！
         throw err;
      }
   }

   // 更新奖励组信息
   public async updateAwardGroup(req: any) {
      const { logger } = this;
      try {
         const sqlStr = ` UPDATE activity_comm_award_group
         SET group_name = ?, group_index = ?
         WHERE id = ? `;
         await this.execSql(sqlStr, [
            req.group_name,
            req.group_index,
            req.id
         ]);
         logger.info(`Updated award group with ID: ${req.id}`);
      } catch (err) {
         logger.error(err);
         throw err;
      }
   }

   public async insertAward(req: any) {
      const { app } = this;
      const db = app.mysql.get('werewolf');
      const conn = await db.beginTransaction();
      try {
         let awardId = 0;
         if (!req.award_id) {
            const sqlSelectMax = ` SELECT (IFNULL(MAX(award_id),0)) AS award_id_new FROM activity_comm_award_conf WHERE activity_id = ? AND award_group_id = ? `;
            const maxResult: any = await conn.query(sqlSelectMax, [req.activity_id, req.award_group_id]);
            if (maxResult[0].award_id_new < req.group_index) {
               awardId = req.group_index;
            } else {
               awardId = maxResult[0].award_id_new;
            }
            awardId = awardId + 1;
         }else{
            awardId = req.award_id;
         }

         const sqlStr = ` INSERT INTO activity_comm_award_conf (
               activity_id,
               award_id,
               award_group_id,
               weight,
               target_num,
               is_limit,
               num,
               award_name,
               max_times,
               \`desc\`,
               is_optional,
               item_dic_id,
               headge_num,
               guarante_num
            ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)  `;
         await this.selectList(sqlStr, [
            req.activity_id,
            awardId,
            req.award_group_id,
            req.weight,
            req.target_num,
            req.is_limit,
            req.num,
            req.award_name,
            req.max_times,
            req.desc,
            req.is_optional,
            req.item_dic_id,
            req.headge_num,
            req.guarante_num
         ]);
         await conn.commit(); // 提交事务
      } catch (err) {
         await conn.rollback(); // 一定记得捕获异常后回滚事务！
         throw err;
      }
   }
   //4 新建一个奖励
   //5 更新一个奖励
   //6 删除一个奖励

   public async getSubItemList(subItemIds: string) {
      const { app } = this;
      try {
         // 将字符串转换为数组并去除空格
         const itemIds = subItemIds.split(',').map(id => id.trim());

         // 查询数据库
         const items = await this.selectList(
            `SELECT
idc.id,
idc.item_id,
idc.item_cate_id,
ic.item_table
FROM item_dic AS idc
LEFT JOIN item_cate AS ic ON ic.id = idc.item_cate_id
WHERE idc.id IN (?)`,
            [itemIds]
         );

         return items;
      } catch (error) {
         this.ctx.logger.error('查询子道具列表失败', error);
         throw error;
      }
   }
}