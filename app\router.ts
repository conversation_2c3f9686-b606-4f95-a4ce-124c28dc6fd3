import { Router, Application } from 'egg'
/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2018-11-20 18:00:10
 * @LastEditors: yunpeng.li <EMAIL>
 * @LastEditTime: 2024-05-15 13:36:19
 */

import { AccessRouteId, AccessRouteName } from './model/accessRouteCof'
import routerHomeDialog from './routerHomeDialog'
import routerAtyConf from './routerAtyConf'
import routerMall from './routerMall'
import routerDeduction from './routerDeduction'
import routerAccountBlacklist from './routerAccountBlacklist'
import routerMallItem from './routerMallItem'
import routerGameArea from './routerGameArea'
import routerNoble from './routerNoble'
import routerLotteryBox from './routerLotteryBox'
import routerPrizePool from './routerPrizePool'
import routerGroupProps from './routerGroupProps'
import routerAnnounce from './routerAnnounce'
// import routerWolfKing from './routerWolfKing'
import routerGameBoard from './routerGameBoard'
import routerUdidInquire from './routerUdidInquire'
import routerScoreGroup from './routerScoreGroup'
import routerUserCredit from './routerUserCredit'
import routerPlayerInfo from './routerPlayerInfo'
import routerTreaseure from './routerTreasure'
import routerNewBanner from './routerNewBanner'
import routerGroupVersus from './routerGroupVersus'
import routerItem from './routerItem'
import routerAnchorBanner from './routerAnchorBanner'
import routerAccountAbnormal from './routerAccountAbnormal'
import router2w from './router2w'
import routerBroadcast from './routerBroadcast'
import routerDesignStar from './routerDesignStar'

import atyAward from './routerAtyAward'
import routerUpdateContent from './routerUpdateContent'

import routerScriptkill from './routerScriptkill'

import routerAnchorShow from './routerAnchorShow'

import routerTalent from './routerTalent'

import routerLockAccount from './routerLockAccount'

import routerArenaActivity from './routerArenaActivity'

import routerUserCollection from './routerUserCollection'

import routerMarket from './routerMarket'

import routerUserBag from './routerUserBag'

import routerHiddenFundOutput from './routerHiddenFundOutput'
import routerMining from './routerMining'
import routerTeamGiftBag from './routerTeamGiftBag'
import routerMiningSeason from './routerMiningSeason'
import routerNewStoneManage from './routerNewStoneManage'

const API_VERSION = '/api'

export default (app: Application) => {
    const { controller, router } = app

    /**
     * @name: 路由权限校验中间件
     * @param:{ routeId:路由id }
     * @return: function
     */
    const accCtr = (routeId: number) => {
        const middleware = app.middleware
        return middleware.accessRoute({ routeId })
    }

    //锦礼袋
    routerTeamGiftBag(API_VERSION, app, accCtr)

    //首页弹框路由
    routerHomeDialog(API_VERSION, app, accCtr)
    routerAtyConf(API_VERSION, app, accCtr)

    // 商城管理路由
    routerMall(API_VERSION, app, accCtr)

    //商城道具管理路由
    routerMallItem(API_VERSION, app, accCtr)

    routerBroadcast(API_VERSION, app, accCtr)

    routerGameArea(API_VERSION, app, accCtr)

    routerNoble(API_VERSION, app, accCtr)

    routerLotteryBox(API_VERSION, app, accCtr)

    routerPrizePool(API_VERSION, app, accCtr)

    routerUserBag(API_VERSION, app, accCtr)

    //商城-公会道具管理
    routerGroupProps(API_VERSION, app, accCtr)

    //系统公告
    routerAnnounce(API_VERSION, app, accCtr)
    //狼王集结
    // routerWolfKing(API_VERSION, app, accCtr)
    //未来周计分
    routerGameBoard(API_VERSION, app, accCtr)
    //玩家信息
    routerPlayerInfo(API_VERSION, app, accCtr)

    //新banner上传
    routerNewBanner(API_VERSION, app, accCtr)
    //公会战
    routerGroupVersus(API_VERSION, app, accCtr)
    //道具管理
    routerItem(API_VERSION, app, accCtr)
    //2w框
    router2w(API_VERSION, app, accCtr)
    //主播板块banner控制
    routerAnchorBanner(API_VERSION, app, accCtr)
    //版本更新弹窗h5编辑
    routerUpdateContent(API_VERSION, app, accCtr)

    //违规用户信息
    routerDeduction(API_VERSION, app, accCtr)
    routerAccountBlacklist(API_VERSION, app, accCtr)
    routerAccountAbnormal(API_VERSION, app, accCtr)
    routerUdidInquire(API_VERSION, app, accCtr)
    routerScoreGroup(API_VERSION, app, accCtr)

    //活动奖励配置
    atyAward(API_VERSION, app, accCtr)

    //剧本杀路由
    routerScriptkill(API_VERSION, app, accCtr)

    // 商城管理路由
    routerUserCredit(API_VERSION, app, accCtr)

    //主播秀场
    routerAnchorShow(API_VERSION, app, accCtr)

    routerTalent(API_VERSION, app, accCtr)
    //设计之星
    routerDesignStar(API_VERSION, app, accCtr)

    //账号封禁
    routerLockAccount(API_VERSION, app, accCtr)
    //竞技场活动
    routerArenaActivity(API_VERSION, app, accCtr)
    //收藏
    routerUserCollection(API_VERSION, app, accCtr)
    //集市-流通详情查询
    routerMarket(API_VERSION, app, accCtr)
    //集市-隐藏款产出查询
    routerHiddenFundOutput(API_VERSION, app, accCtr)
    routerMining(API_VERSION, app, accCtr)
    //集市-赛季图鉴
    routerMiningSeason(API_VERSION, app, accCtr)
    //集市-新框石管理
    routerNewStoneManage(API_VERSION, app, accCtr)
    //router.get(`/`, controller.home.index);
    //登陆
    router.get(`${API_VERSION}/user/env`, controller.user.env)
    router.get(`${API_VERSION}/user/publicKey`, controller.user.publicKey)
    router.post(`${API_VERSION}/user/auth`, controller.user.auth)
    //router.post(`${API_VERSION}/user/login`, controller.user.login);
    //manager目录
    router.get(`${API_VERSION}/manager/:id/info`, controller.user.getInfo)
    router.post(`${API_VERSION}/manager/logoutAll`, controller.user.logOutAll)
    router.get(`${API_VERSION}/manager/userList`, controller.user.userList)
    router.post(`${API_VERSION}/manager/analysis/resetPwd`, controller.user.resetPwd)
    //删除管理员
    router.post(`${API_VERSION}/manager/Deleteuser`, accCtr(AccessRouteId.admin_edit_route), controller.user.userdelete)
    //創建管理員
    router.post(`${API_VERSION}/manager/Createuser`, accCtr(AccessRouteId.admin_edit_route), controller.user.usercreate)
    //权限管理
    router.post(`${API_VERSION}/manager/access/routes`, accCtr(AccessRouteId.admin_edit_route), controller.access.routes)
    router.post(`${API_VERSION}/manager/access/getUserAccessList`, accCtr(AccessRouteId.admin_edit_route), controller.access.getUserAccessList)
    router.post(`${API_VERSION}/manager/access/update`, accCtr(AccessRouteId.admin_edit_route), controller.access.update)

    //狼人杀
    //【玩家资产】
    routerTreaseure(API_VERSION, app, accCtr)
    //【玩家资产】详细资产-头像框
    router.post(
        `${API_VERSION}/werewolf/treasure/updateFrameDelsign`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.updateFrameDelsign,
    )
    router.post(
        `${API_VERSION}/werewolf/treasure/uploadLettering`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.uploadLettering,
    )
    router.post(
        `${API_VERSION}/werewolf/treasure/delLettering`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.delLettering,
    )

    router.post(
        `${API_VERSION}/werewolf/treasure/updateUserLettering`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.updateUserLettering,
    )
    //【玩家资产】详细资产-成就
    router.post(
        `${API_VERSION}/werewolf/treasure/updateAchieveDelsign`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.updateAchieveDelsign,
    )
    //【玩家资产】资产-红包情况
    router.post(`${API_VERSION}/werewolf/treasure/redbag`, accCtr(AccessRouteId.wolf_treasure_detail), controller.werewolf.treasure.redbagDetail)
    router.get(`${API_VERSION}/werewolf/treasure/tabnormalDescs`, controller.werewolf.treasure.tabnormalDescs)

    //【玩家状态】
    router.post(
        `${API_VERSION}/werewolf/playerStatus/resetAvatar`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.resetAvatar,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/updateNickname`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.updateNickname,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/updateScore`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.updateScore,
    )
    router.post(`${API_VERSION}/werewolf/playerStatus/default`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.playerStatus.defalut)
    router.post(
        `${API_VERSION}/werewolf/playerStatus/getPlayerId`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.getPlayerId,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/getPhoneNumber`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.getPhoneNumber,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/brushScore`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.brushScore,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/newBrushScore`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.newBrushScore,
    )
    router.post(`${API_VERSION}/werewolf/playerStatus/escape`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.playerStatus.escape)
    router.post(`${API_VERSION}/werewolf/playerStatus/gameSpeak`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.playerStatus.gameSpeak)
    router.post(`${API_VERSION}/werewolf/playerStatus/playerBg`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.playerStatus.playerBg)
    router.post(`${API_VERSION}/werewolf/playerStatus/report`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.playerStatus.report)
    router.post(
        `${API_VERSION}/werewolf/playerStatus/report/video`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.reportVideoList,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/illegalImages`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.illegalImages,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/list/shutter`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.getShutterList,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/list/boardcastImprison`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.getBoardcastImpList,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/doBanned`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.doBanned,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/getUserBanId`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.getUserBanId,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/doShutter`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.doShutter,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/dobarrage`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.dobarrage,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/doMobile`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.doMobile,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/doBanEntertainment`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.doBanEntertainment,
    )

    /*娱乐模式*/
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchReportUser`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchReportUser,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchReportRoom`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchReportRoom,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchGroupRooms`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchGroupRooms,
    )

    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchReportUserDetail`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchReportUserDetail,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchRoomUsers`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchRoomUsers,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchRoomReports`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchRoomReports,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/searchRoomChats`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentSearchRoomChats,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/entertainment/kickOutRoom`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.entertainmentKickOutRoom,
    )

    router.post(
        `${API_VERSION}/werewolf/playerStatus/getIdCardList`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.getIdCardList,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/setIdCardBan`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.setIdCardBan,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/delIdCardBan`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.delIdCardBan,
    )

    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/leftShutter`,
        accCtr(AccessRouteId.wolf_status_update),
        controller.werewolf.playerStatus.leftShutter,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/bannedBg`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.bannedBg,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/remove`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.remove,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/banUserAbility`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.banUserAbility,
    ) //封禁功能
    router.post(
        `${API_VERSION}/werewolf/playerStatus/analysis/removeForeverBlock`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.removeForeverBlock,
    ) //解封永久功能封禁

    //【玩家状态】清逃跑
    router.post(`${API_VERSION}/werewolf/clearEscape/list`, accCtr(AccessRouteId.wolf_clear_escape), controller.werewolf.clearEscape.getEscapeList)

    router.post(`${API_VERSION}/werewolf/clearEscape/do`, accCtr(AccessRouteId.wolf_clear_escape), controller.werewolf.clearEscape.do)
    //【玩家状态】udid查询
    router.post(
        `${API_VERSION}/werewolf/udidQuery/getUdidQueryList`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.udidQuery.getUdidQueryList,
    )
    router.post(
        `${API_VERSION}/werewolf/udidQuery/userVerifyStatus`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.udidQuery.userVerifyStatus,
    )

    router.post(
        `${API_VERSION}/werewolf/udidQuery/removeIdentifyByUdid`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.udidQuery.removeIdentifyByUdid,
    )

    // 【玩家状态】用户认证
    router.post(
        `${API_VERSION}/werewolf/identification/identify`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.identification.identify,
    )
    router.post(
        `${API_VERSION}/werewolf/identification/identifyByUdid`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.identification.identifyByUdid,
    )
    router.post(
        `${API_VERSION}/werewolf/identification/cancelIdentify`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.identification.cancelIdentify,
    )
    // 【玩家状态】弹幕封禁
    router.post(
        `${API_VERSION}/werewolf/banDanMu/getBanDanMuList`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.banDanMu.getBanDanMuList,
    )
    //【玩家状态】竞技场
    router.post(
        `${API_VERSION}/werewolf/arenaArea/getArenaAreaList`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.arenaArea.getArenaAreaList,
    )

    // 【玩家状态】阿里udid
    router.post(`${API_VERSION}/werewolf/aliUmid/getAliUmidList`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.aliUmid.getAliUmidList)
    router.post(`${API_VERSION}/werewolf/aliUmid/banUserAliUmid`, accCtr(AccessRouteId.wolf_status_query), controller.werewolf.aliUmid.banUserAliUmid)

    //【应用管理】
    //【应用管理】广告位管理
    router.post(`${API_VERSION}/werewolf/advertising/list`, accCtr(AccessRouteId.wolf_ad), controller.werewolf.advertising.list)
    router.post(`${API_VERSION}/werewolf/advertising/analysis/operation`, accCtr(AccessRouteId.wolf_ad), controller.werewolf.advertising.operation)
    //【应用管理】开屏管理
    router.post(
        `${API_VERSION}/werewolf/advertising/addScreenAdImg`,
        accCtr(AccessRouteId.wolf_splash),
        controller.werewolf.advertising.addScreenAdImg,
    ) //添加开屏图
    router.post(
        `${API_VERSION}/werewolf/advertising/delScreenAdImg`,
        accCtr(AccessRouteId.wolf_splash),
        controller.werewolf.advertising.delScreenAdImg,
    ) //删除开屏图
    router.post(`${API_VERSION}/werewolf/advertising/saveAdInfo`, accCtr(AccessRouteId.wolf_splash), controller.werewolf.advertising.saveAdInfo) //保存开屏图信息
    //【应用管理】头像框管理
    router.post(`${API_VERSION}/werewolf/avatarframe/userName`, controller.werewolf.avatarFrame.userName) //输入id查询用户名
    router.post(`${API_VERSION}/werewolf/avatarframe/getAllFrames`, controller.werewolf.avatarFrame.getAllFrames) //获取所有头像框(根据输入条件)
    router.post(`${API_VERSION}/werewolf/avatarframe/getStaticAvatarFrameList`, controller.werewolf.avatarFrame.getStaticAvatarFrameList) //获取所有头像框(根据输入条件)
    router.post(
        `${API_VERSION}/werewolf/avatarframe/giveFrameToUser`,
        accCtr(AccessRouteId.wolf_avatar_frame),
        controller.werewolf.avatarFrame.giveFrameToUser,
    ) //头像框给与目标用户
    router.post(
        `${API_VERSION}/werewolf/avatarframe/frameIsShow`,
        accCtr(AccessRouteId.wolf_avatar_frame),
        controller.werewolf.avatarFrame.frameIsShow,
    ) //操作头像框(上架/下架)
    router.post(
        `${API_VERSION}/werewolf/avatarframe/uploadAvatarFrame`,
        accCtr(AccessRouteId.wolf_avatar_frame),
        controller.werewolf.avatarFrame.uploadAvatarFrame,
    ) //上传头像框基本信息
    router.post(
        `${API_VERSION}/werewolf/avatarframe/uploadFrameComplete`,
        accCtr(AccessRouteId.wolf_avatar_frame),
        controller.werewolf.avatarFrame.uploadFrameComplete,
    ) //上传完毕改变头像框完成度
    //【应用管理】成就
    router.post(`${API_VERSION}/werewolf/achievement/getList`, accCtr(AccessRouteId.wolf_achievement), controller.werewolf.achievement.getList) //获取所有成就
    router.post(
        `${API_VERSION}/werewolf/achievement/updateDelsign`,
        accCtr(AccessRouteId.wolf_achievement),
        controller.werewolf.achievement.updateDelsign,
    ) //上架/下架应用内成就
    router.post(
        `${API_VERSION}/werewolf/achievement/sendAchieve`,
        accCtr(AccessRouteId.wolf_achievement),
        controller.werewolf.achievement.sendAchieve,
    ) //发送成就
    router.post(`${API_VERSION}/werewolf/achievement/uploadBase`, accCtr(AccessRouteId.wolf_achievement), controller.werewolf.achievement.uploadBase) //更新成就基本信息
    router.post(`${API_VERSION}/werewolf/achievement/complete`, accCtr(AccessRouteId.wolf_achievement), controller.werewolf.achievement.complete) //更新成就基本信息
    router.post(
        `${API_VERSION}/werewolf/achievement/completeNoteOrder`,
        accCtr(AccessRouteId.wolf_achievement),
        controller.werewolf.achievement.completeNoteOrder,
    ) //更新成就基本信息 头像框刻字

    //【应用管理】活动状态管理
    router.post(
        `${API_VERSION}/werewolf/activity/getActivityList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.activityState.getActivityList,
    ) //获取活动列表
    router.post(`${API_VERSION}/werewolf/activity/operHallState`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.activityState.operHallState) //大厅入口显示隐藏
    router.post(
        `${API_VERSION}/werewolf/activity/operActivityState`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.activityState.operActivityState,
    ) //活动开启与关闭
    router.post(
        `${API_VERSION}/werewolf/activity/changeActivityName`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.activityState.changeActivityName,
    ) //更改活动名称
    router.post(
        `${API_VERSION}/werewolf/activity/changeActivityPrizeNum`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.activityState.changeActivityPrizeNum,
    ) //更改活动奖励数量
    //【应用管理】发送邮件
    router.post(
        `${API_VERSION}/werewolf/sendEmail/sendEmailsToUsers`,
        accCtr(AccessRouteId.wolf_email),
        controller.werewolf.sendEmail.sendEmailsToUsers,
    ) //给用户发送邮件
    //【应用管理】banner图
    router.post(`${API_VERSION}/werewolf/bannerControl/bannerList`, controller.werewolf.bannerControl.bannerList) //banner图
    router.post(`${API_VERSION}/werewolf/bannerControl/uploadBannerInfo`, controller.werewolf.bannerControl.uploadBannerInfo) //上传banner信息
    router.post(`${API_VERSION}/werewolf/bannerControl/bannerIsShow`, controller.werewolf.bannerControl.bannerIsShow) //控制banner显示隐藏
    router.post(`${API_VERSION}/werewolf/bannerControl/delBannerInfo`, controller.werewolf.bannerControl.delBannerInfo) //删除banner信息
    //灰度用banner
    router.post(`${API_VERSION}/werewolf/bannerControl/getBetaBannerList`, controller.werewolf.bannerControl.getBetaBannerList)
    router.post(`${API_VERSION}/werewolf/bannerControl/addBetaBanner`, controller.werewolf.bannerControl.addBetaBanner)
    router.post(`${API_VERSION}/werewolf/bannerControl/updateBetaBannerInfo`, controller.werewolf.bannerControl.updateBetaBannerInfo)

    //【应用管理】推送
    //发送三天前新注册用户一直没开局的
    router.post(`${API_VERSION}/werewolf/push/newUser`, accCtr(AccessRouteId.wolf_push), controller.werewolf.push.searchNewUserList)
    //发送老用户沉默15天未登录的（老用户标准：开局10局以上）
    router.post(`${API_VERSION}/werewolf/push/oldUser15`, accCtr(AccessRouteId.wolf_push), controller.werewolf.push.searchOldUserFifthDays)
    //老用户沉默30天未登录的（老用户标准：开局10局以上）
    router.post(`${API_VERSION}/werewolf/push/oldUser30`, accCtr(AccessRouteId.wolf_push), controller.werewolf.push.searchOldUserThirtyDays)
    //发送给某个用户发推送
    router.post(`${API_VERSION}/werewolf/push/customUser`, accCtr(AccessRouteId.wolf_push), controller.werewolf.push.sendMsgToSomebody)

    //【应用管理】推送V2
    //新建推送
    router.post(`${API_VERSION}/werewolf/pushV2/create`, accCtr(AccessRouteId.wolf_push), controller.werewolf.pushCreate.create)
    //推送列表
    router.post(`${API_VERSION}/werewolf/pushV2/list`, accCtr(AccessRouteId.wolf_push), controller.werewolf.pushCreate.list)
    //编辑推送
    router.post(`${API_VERSION}/werewolf/pushV2/edit`, accCtr(AccessRouteId.wolf_push), controller.werewolf.pushCreate.edit)
    //delete推送
    router.post(`${API_VERSION}/werewolf/pushV2/delete`, accCtr(AccessRouteId.wolf_push), controller.werewolf.pushCreate.delete)
    //测试推送服务联通性
    router.get(`${API_VERSION}/werewolf/pushV2/test`, accCtr(AccessRouteId.wolf_push), controller.werewolf.pushCreate.test)

    //测试
    router.get(`/download/png`, controller.home.downloadPng)

    //【游戏记录】
    //玩家游戏列表
    router.post(`${API_VERSION}/werewolf/gameRecord/list`, accCtr(AccessRouteId.wolf_record_game), controller.werewolf.gameRecord.list)
    router.post(
        `${API_VERSION}/werewolf/gameRecord/gameInfoList`,
        accCtr(AccessRouteId.wolf_record_game),
        controller.werewolf.gameRecord.gameInfoList,
    )
    router.post(
        `${API_VERSION}/werewolf/gameRecord/downloadGameInfo`,
        accCtr(AccessRouteId.wolf_record_game),
        controller.werewolf.gameRecord.downloadGameInfo,
    )

    //游戏详情
    router.post(`${API_VERSION}/werewolf/gameRecord/detail`, accCtr(AccessRouteId.wolf_record_game), controller.werewolf.gameRecord.detail)
    //游戏复盘
    router.post(`${API_VERSION}/werewolf/gameRecord/replay`, controller.werewolf.gameRecord.replay)
    //融云记录查询
    router.post(`${API_VERSION}/werewolf/rongCloud/info`, accCtr(AccessRouteId.wolf_record_im), controller.werewolf.rongCloud.info)
    //世界频道 聊天列表
    router.post(`${API_VERSION}/werewolf/broadcast/list`, accCtr(AccessRouteId.wolf_record_broardcast), controller.werewolf.broadcast.recordList)
    //世界频道 屏蔽某聊天记录
    router.post(`${API_VERSION}/werewolf/broadcast/ban`, accCtr(AccessRouteId.wolf_record_broardcast), controller.werewolf.broadcast.ban)
    //邮件查询
    router.post(`${API_VERSION}/werewolf/broadcast/tnotice`, accCtr(AccessRouteId.wolf_record_broardcast), controller.werewolf.broadcast.tnotice)
    //身份证信息查询
    router.post(
        `${API_VERSION}/werewolf/broadcast/idCardInfoList`,
        accCtr(AccessRouteId.wolf_record_broardcast),
        controller.werewolf.broadcast.idCardInfoList,
    )
    //更新份身证信息
    router.post(
        `${API_VERSION}/werewolf/broadcast/updateIdCardInfo`,
        accCtr(AccessRouteId.wolf_record_broardcast),
        controller.werewolf.broadcast.updateIdCardInfo,
    )

    //【操作流水】
    router.post(`${API_VERSION}/werewolf/operation/propCount`, accCtr(AccessRouteId.wolf_operation), controller.werewolf.operation.propCount)
    router.post(`${API_VERSION}/werewolf/operation/propList`, accCtr(AccessRouteId.wolf_operation), controller.werewolf.operation.propList)
    //玩家状态操作流水
    router.post(`${API_VERSION}/werewolf/operation/bannedList`, accCtr(AccessRouteId.wolf_operation), controller.werewolf.operation.bannedList)
    router.post(`${API_VERSION}/werewolf/operation/getOptionList`, accCtr(AccessRouteId.wolf_operation), controller.werewolf.operation.getOptionList)
    router.post(
        `${API_VERSION}/werewolf/operation/avatarFrameList`,
        accCtr(AccessRouteId.wolf_operation),
        controller.werewolf.operation.avatarFrameList,
    ) //操作流水
    router.post(`${API_VERSION}/werewolf/operation/achieveList`, accCtr(AccessRouteId.wolf_operation), controller.werewolf.operation.achieveList) //操作流水

    //了了
    router.post(`${API_VERSION}/aiim/withdraw/list`, accCtr(AccessRouteId.liao_drawings), controller.aiim.withdraw.list)

    //【开发】
    //接口文档列表
    router.get(`${API_VERSION}/dev/apidoc/list`, accCtr(AccessRouteId.dev_swagger), controller.dev.apiDoc.list)
    router.post(
        `${API_VERSION}/dev/server/monitor/type/list`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.getServerMonitorTypeList,
    )
    router.post(
        `${API_VERSION}/dev/server/monitor/user/list`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.getServerMonitorUserList,
    )
    router.post(
        `${API_VERSION}/dev/server/monitor/business/list`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.getServerMonitorBusinessList,
    )
    router.post(
        `${API_VERSION}/dev/server/monitor/server/list`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.getServerMonitorServerList,
    )
    router.post(`${API_VERSION}/dev/server/monitor/env/list`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.getServerMonitorEnvList)
    router.post(
        `${API_VERSION}/dev/server/monitor/format/list`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.getServerMonitorFormatList,
    )
    router.post(
        `${API_VERSION}/dev/server/monitor/system/list`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.getServerMonitorSystemList,
    )
    router.post(`${API_VERSION}/dev/server/monitor/list`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.getServerMonitorList)
    router.post(`${API_VERSION}/dev/server/monitor/update`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateServerMonitor)
    router.post(
        `${API_VERSION}/dev/server/monitor/update/single`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.updateServerMonitorSingleData,
    )
    router.post(`${API_VERSION}/dev/server/monitor/insert`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertServerMonitor)
    router.post(
        `${API_VERSION}/dev/server/monitor/update/delsign`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.updateServerMonitorDelsign,
    )
    router.post(
        `${API_VERSION}/dev/server/monitor/update/log/delsign`,
        accCtr(AccessRouteId.dev_server_monitor),
        controller.dev.apiDoc.updateServerMonitorLogDelsign,
    )
    router.post(`${API_VERSION}/dev/server/monitor/insert/server`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertServer)
    router.post(`${API_VERSION}/dev/server/monitor/insert/business`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertBusiness)
    router.post(`${API_VERSION}/dev/server/monitor/insert/env`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertEnv)
    router.post(`${API_VERSION}/dev/server/monitor/insert/type`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertType)
    router.post(`${API_VERSION}/dev/server/monitor/insert/user`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertUser)
    router.post(`${API_VERSION}/dev/server/monitor/insert/format`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertFormat)
    router.post(`${API_VERSION}/dev/server/monitor/insert/system`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.insertSystem)
    router.post(`${API_VERSION}/dev/server/monitor/update/server`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateServer)
    router.post(`${API_VERSION}/dev/server/monitor/update/business`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateBusiness)
    router.post(`${API_VERSION}/dev/server/monitor/update/env`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateEnv)
    router.post(`${API_VERSION}/dev/server/monitor/update/type`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateType)
    router.post(`${API_VERSION}/dev/server/monitor/update/user`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateUser)
    router.post(`${API_VERSION}/dev/server/monitor/update/format`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateFormat)
    router.post(`${API_VERSION}/dev/server/monitor/update/system`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.updateSystem)
    router.post(`${API_VERSION}/dev/server/monitor/delete/server`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteServer)
    router.post(`${API_VERSION}/dev/server/monitor/delete/business`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteBusiness)
    router.post(`${API_VERSION}/dev/server/monitor/delete/env`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteEnv)
    router.post(`${API_VERSION}/dev/server/monitor/delete/type`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteType)
    router.post(`${API_VERSION}/dev/server/monitor/delete/user`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteUser)
    router.post(`${API_VERSION}/dev/server/monitor/delete/format`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteFormat)
    router.post(`${API_VERSION}/dev/server/monitor/delete/system`, accCtr(AccessRouteId.dev_server_monitor), controller.dev.apiDoc.deleteSystem)

    //宝箱管理
    router.post(`${API_VERSION}/werewolf/gameConfig/openList`, accCtr(AccessRouteId.wolf_box_manager), controller.werewolf.gameConfig.getOpenList)
    router.post(`${API_VERSION}/werewolf/gameConfig/updateOpen`, accCtr(AccessRouteId.wolf_box_manager), controller.werewolf.gameConfig.updateOpen)
    router.post(`${API_VERSION}/werewolf/gameConfig/createOpen`, accCtr(AccessRouteId.wolf_box_manager), controller.werewolf.gameConfig.createOpen)
    router.post(
        `${API_VERSION}/werewolf/gameConfig/createOpenList`,
        accCtr(AccessRouteId.wolf_box_manager),
        controller.werewolf.gameConfig.createOpenList,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/getTboxSeason`,
        accCtr(AccessRouteId.wolf_box_manager),
        controller.werewolf.gameConfig.getTboxSeasonList,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/updateEndTime`,
        accCtr(AccessRouteId.wolf_box_manager),
        controller.werewolf.gameConfig.updateEndTime,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/createNewSeason`,
        accCtr(AccessRouteId.wolf_box_manager),
        controller.werewolf.gameConfig.createNewSeason,
    )
    router.get(`${API_VERSION}/werewolf/gameConfig/getBoxList`, accCtr(AccessRouteId.wolf_box_manager), controller.werewolf.gameConfig.getBoxList)
    router.get(
        `${API_VERSION}/werewolf/gameConfig/getAllBoxList`,
        accCtr(AccessRouteId.wolf_box_manager),
        controller.werewolf.gameConfig.getAllBoxList,
    )

    //道具接口
    router.get(`${API_VERSION}/werewolf/gameConfig/getProps`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.gameConfig.getProps)
    router.get(`${API_VERSION}/werewolf/gameConfig/getAllProps`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.gameConfig.getAllProps)
    router.post(
        `${API_VERSION}/werewolf/gameConfig/updateProps`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.updateProps,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/insertProps`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.insertProps,
    )

    //动效接口
    router.get(
        `${API_VERSION}/werewolf/gameConfig/getAnimation`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.getAnimation,
    )
    router.get(
        `${API_VERSION}/werewolf/gameConfig/getAnimationRole`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.getAnimationRole,
    )
    router.post(`${API_VERSION}/werewolf/gameConfig/updateAni`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.gameConfig.updateAni)
    router.post(`${API_VERSION}/werewolf/gameConfig/insertAni`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.gameConfig.insertAni)
    router.post(
        `${API_VERSION}/werewolf/gameConfig/insertDocumentResource`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.insertDocumentResource,
    )

    //头像框接口
    router.get(`${API_VERSION}/werewolf/gameConfig/getFrame`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.gameConfig.getFrame)
    router.get(
        `${API_VERSION}/werewolf/gameConfig/getFrameWithItemDic`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.getFrameWithItemDic,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/updateFrame`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.updateFrame,
    )

    //礼包
    router.get(
        `${API_VERSION}/werewolf/gameConfig/giftBagInfo`,
        accCtr(AccessRouteId.wolf_gift_bag_manager),
        controller.werewolf.gameConfig.getGiftBagInfo,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/updateBagInfo`,
        accCtr(AccessRouteId.wolf_gift_bag_manager),
        controller.werewolf.gameConfig.updateBagInfo,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/createBagInfo`,
        accCtr(AccessRouteId.wolf_gift_bag_manager),
        controller.werewolf.gameConfig.createBagInfo,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/updateBagInfoContent`,
        accCtr(AccessRouteId.wolf_gift_bag_manager),
        controller.werewolf.gameConfig.updateBagContentInfo,
    )
    router.post(
        `${API_VERSION}/werewolf/gameConfig/createBagInfoContent`,
        accCtr(AccessRouteId.wolf_gift_bag_manager),
        controller.werewolf.gameConfig.createBagContentInfo,
    )

    //账号变更
    router.post(
        `${API_VERSION}/werewolf/playerStatus/getMemberChange`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.getMemberChange,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/checkUserExist`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.checkUserExist,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/insertMemberChange`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.insertMemberChange,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/updateMemberChange`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.updateMemberChange,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/checkUserExistName`,
        accCtr(AccessRouteId.wolf_member_change_confirm),
        controller.werewolf.playerStatus.checkUserExistName,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/updateMemberChangeAndUserName`,
        accCtr(AccessRouteId.wolf_member_change_confirm),
        controller.werewolf.playerStatus.updateMemberChangeAndUserName,
    )
    router.post(
        `${API_VERSION}/werewolf/playerStatus/downloadRestoreOSS`,
        accCtr(AccessRouteId.wolf_status_query),
        controller.werewolf.playerStatus.downloadRestoreOSS,
    ) //解封永久功能封禁

    //【游戏板子V2】
    //open板子列表
    router.get(`${API_VERSION}/werewolf/gameBoard/openList`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.gameBoard.getOpenList)
    //不在open的板子id
    router.get(`${API_VERSION}/werewolf/gameBoard/unOpenList`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.gameBoard.geUnOpenList)
    router.post(`${API_VERSION}/werewolf/gameBoard/updateOpen`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.gameBoard.updateOpen)
    router.post(`${API_VERSION}/werewolf/gameBoard/createOpen`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.gameBoard.createOpen)
    router.post(
        `${API_VERSION}/werewolf/gameBoard/updateOpenSort`,
        accCtr(AccessRouteId.wolf_game_config),
        controller.werewolf.gameBoard.updateOpenSort,
    )
    router.post(`${API_VERSION}/werewolf/gameBoard/deleteOpen`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.gameBoard.deleteOpen)

    //获取用户匹配值
    router.post(
        `${API_VERSION}/werewolf/gameConfig/userScoreList`,
        accCtr(AccessRouteId.wolf_props_manager),
        controller.werewolf.gameConfig.getUserScoreList,
    )

    router.post(
        `${API_VERSION}/werewolf/activity/getWordActivityList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.getWordActivityList,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/getWordActivityListCount`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.getWordActivityListCount,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/getWordAwardList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.getWordAwardList,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/updateWordActivity`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.updateWordActivity,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/updateWordActivityState`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.updateWordActivityState,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/insertWordActivity`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.insertWordActivity,
    )
    router.post(`${API_VERSION}/werewolf/activity/getWordBoxList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.wordActivity.getWordBoxList)
    router.post(
        `${API_VERSION}/werewolf/activity/insertWordAward`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.insertWordAward,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/updateWordAward`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.updateWordAward,
    )
    router.post(`${API_VERSION}/werewolf/activity/insertWordBox`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.wordActivity.insertWordBox)
    router.post(`${API_VERSION}/werewolf/activity/updateWordBox`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.wordActivity.updateWordBox)
    router.post(
        `${API_VERSION}/werewolf/activity/updateWordBoxDelsign`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.updateWordBoxDelsign,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/getWordJumpList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.getWordJumpList,
    )
    router.post(`${API_VERSION}/werewolf/activity/updateWordJump`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.wordActivity.updateWordJump)
    router.post(
        `${API_VERSION}/werewolf/activity/updateWordAcitvityImg`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.updateWordAcitvityImg,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/getWordAcitvityImgList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.wordActivity.getWordAcitvityImgList,
    )
    router.post(`${API_VERSION}/werewolf/activity/getWordInfo`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.wordActivity.getWordInfo)
    router.post(`${API_VERSION}/werewolf/activity/updateWordInfo`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.wordActivity.updateWordInfo)

    //集市官方账号 收入 支出
    router.post(
        `${API_VERSION}/werewolf/market/getOfficerAccountDesc`,
        accCtr(AccessRouteId.wolf_market_mine),
        controller.werewolf.officerAccout.getOfficerAccoutDesc,
    )

    //大厅弹窗
    router.post(`${API_VERSION}/werewolf/hallPopups/addRecord`, accCtr(AccessRouteId.wolf_ad), controller.werewolf.hallPopups.addRecord)
    router.post(`${API_VERSION}/werewolf/hallPopups/loadRecord`, accCtr(AccessRouteId.wolf_ad), controller.werewolf.hallPopups.loadRecord)
    router.post(`${API_VERSION}/werewolf/hallPopups/updateRecord`, accCtr(AccessRouteId.wolf_ad), controller.werewolf.hallPopups.updateRecord)
    router.post(`${API_VERSION}/werewolf/hallPopups/deleteRecord`, accCtr(AccessRouteId.wolf_ad), controller.werewolf.hallPopups.deleteRecord)

    //礼袋
    router.post(`${API_VERSION}/werewolf/giftItemList/getGiftItems`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.giftItemList.getGiftItems)
    router.post(`${API_VERSION}/werewolf/giftItemList/addGiftItem`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.giftItemList.addGiftItem)
    router.post(
        `${API_VERSION}/werewolf/giftItemList/updateGiftItem`,
        accCtr(AccessRouteId.wolf_mall),
        controller.werewolf.giftItemList.updateGiftItem,
    )
    router.post(`${API_VERSION}/werewolf/giftItemList/getGifts`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.giftItemList.getGifts)

    router.post(`${API_VERSION}/werewolf/teamWeekGiftBag/getItems`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamWeekGiftBag.getItems)
    router.post(`${API_VERSION}/werewolf/teamWeekGiftBag/addItem`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamWeekGiftBag.addItem)
    router.post(`${API_VERSION}/werewolf/teamWeekGiftBag/updateItem`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamWeekGiftBag.updateItem)

    router.post(`${API_VERSION}/werewolf/userAdvice/getList`, accCtr(AccessRouteId.app_wolf), controller.werewolf.userAdvice.getList)

    router.post(`${API_VERSION}/werewolf/userMood/getList`, accCtr(AccessRouteId.app_wolf), controller.werewolf.userMood.getList)
    router.post(`${API_VERSION}/werewolf/userMood/update`, accCtr(AccessRouteId.app_wolf), controller.werewolf.userMood.update)

    router.post(`${API_VERSION}/werewolf/noviceGroup/getGroups`, accCtr(AccessRouteId.app_wolf), controller.werewolf.noviceGroup.getGroups)
    router.post(`${API_VERSION}/werewolf/noviceGroup/getUsers`, accCtr(AccessRouteId.app_wolf), controller.werewolf.noviceGroup.getUsers)
    router.post(`${API_VERSION}/werewolf/noviceGroup/addUser`, accCtr(AccessRouteId.app_wolf), controller.werewolf.noviceGroup.addUser)
    router.post(`${API_VERSION}/werewolf/noviceGroup/kickUser`, accCtr(AccessRouteId.app_wolf), controller.werewolf.noviceGroup.kickUser)

    router.post(`${API_VERSION}/werewolf/user/removePwdLimit`, accCtr(AccessRouteId.app_wolf), controller.werewolf.user.removePwdLimit)

    //世界频道
    router.post(
        `${API_VERSION}/werewolf/broadcastRedPacket/getList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.broadcastRedPacket.getList,
    )
    router.post(`${API_VERSION}/werewolf/broadcastRedPacket/insert`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.broadcastRedPacket.insert)
    router.post(`${API_VERSION}/werewolf/broadcastRedPacket/update`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.broadcastRedPacket.update)
    router.post(
        `${API_VERSION}/werewolf/broadcastRedPacket/updateType`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.broadcastRedPacket.updateType,
    )

    //新手辅助机器人
    router.post(`${API_VERSION}/werewolf/newPlayerRobot/getList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.newPlayerRobot.getList)
    router.post(
        `${API_VERSION}/werewolf/newPlayerRobot/searchFromList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.newPlayerRobot.searchFromList,
    )
    router.post(`${API_VERSION}/werewolf/newPlayerRobot/insert`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.newPlayerRobot.insert)
    router.post(`${API_VERSION}/werewolf/newPlayerRobot/update`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.newPlayerRobot.update)
    router.post(`${API_VERSION}/werewolf/newPlayerRobot/updateType`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.newPlayerRobot.updateType)

    //生成长图链接
    router.post(`${API_VERSION}/werewolf/illustratedBook/getList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.illustratedBook.getList)
    router.post(
        `${API_VERSION}/werewolf/illustratedBook/fetchFilterItems`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.illustratedBook.fetchFilterItems,
    )
    router.post(
        `${API_VERSION}/werewolf/illustratedBook/searchFromList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.illustratedBook.searchFromList,
    )
    router.post(`${API_VERSION}/werewolf/illustratedBook/insert`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.illustratedBook.insert)
    router.post(`${API_VERSION}/werewolf/illustratedBook/update`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.illustratedBook.update)
    router.post(
        `${API_VERSION}/werewolf/illustratedBook/updateType`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.illustratedBook.updateType,
    )
    router.post(
        `${API_VERSION}/werewolf/illustratedBook/updateItemDelSign`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.illustratedBook.updateItemDelSign,
    )
    router.post(
        `${API_VERSION}/werewolf/illustratedBook/insertPopItem`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.illustratedBook.insertPopItem,
    )
    router.post(
        `${API_VERSION}/werewolf/illustratedBook/updateItemPop`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.illustratedBook.updateItemPop,
    )

    //头像框设置ss级时间
    router.post(`${API_VERSION}/werewolf/ssFrameTime/searchSSFrames`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.ssFrameTime.searchSSFrames)
    router.post(`${API_VERSION}/werewolf/ssFrameTime/updateSSFrameTime`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.ssFrameTime.updateSSFrameTime)


    //甜蜜赠礼
    router.post(
        `${API_VERSION}/werewolf/sweetnessGift/getSweetnesskGiftList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.sweetnessGift.getSweetnesskGiftList,
    )
    router.post(`${API_VERSION}/werewolf/sweetnessGift/insert`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.sweetnessGift.insert)
    router.post(`${API_VERSION}/werewolf/sweetnessGift/update`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.sweetnessGift.update)
    router.post(
        `${API_VERSION}/werewolf/sweetnessGift/updateItemDelSign`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.sweetnessGift.updateItemDelSign,
    )
    router.post(
        `${API_VERSION}/werewolf/sweetnessGift/updateAwardBg`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.sweetnessGift.updateAwardBg,
    )

    //留言板
    router.post(
        `${API_VERSION}/werewolf/messageBoard/getMessageBoardList`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.messageBoard.getMessageBoardList,
    )
    router.post(
        `${API_VERSION}/werewolf/messageBoard/delMessageOrReply`,
        accCtr(AccessRouteId.wolf_redbag),
        controller.werewolf.messageBoard.delMessageOrReply,
    )

    //mst
    //根据ID搜索模型
    router.post(
        `${API_VERSION}/werewolf/mstAddImage/searchFromStableList`,
        accCtr(AccessRouteId.app_mst),
        controller.werewolf.mstAddImage.searchFromStableList,
    )
    router.post(
        `${API_VERSION}/werewolf/mstAddImage/getLikeModelName`,
        accCtr(AccessRouteId.app_mst),
        controller.werewolf.mstAddImage.getLikeModelName,
    )
    router.post(`${API_VERSION}/werewolf/mstAddImage/getVaeList`, accCtr(AccessRouteId.app_mst), controller.werewolf.mstAddImage.getVaeList)
    router.post(`${API_VERSION}/werewolf/mstAddImage/getSamplerList`, accCtr(AccessRouteId.app_mst), controller.werewolf.mstAddImage.getSamplerList)
    router.post(
        `${API_VERSION}/werewolf/mstAddImage/insertMstImageInfo`,
        accCtr(AccessRouteId.app_mst),
        controller.werewolf.mstAddImage.insertMstImageInfo,
    )
    // router.post(`${API_VERSION}/werewolf/mstAddImage/updateMstImageInfo`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.mstAddImage.updateMstImageInfo);

    //模型管理
    //模型类型、基础模型、可见范围、推荐
    router.post(`${API_VERSION}/werewolf/setModel/searchModelInfo`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.searchModelInfo)

    router.post(`${API_VERSION}/werewolf/setModel/searchFromModelList`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.searchFromModelList)
    router.post(`${API_VERSION}/werewolf/setModel/delPic`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.delPic)
    router.post(`${API_VERSION}/werewolf/setModel/addPic`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.addPic)
    //类型
    router.post(`${API_VERSION}/werewolf/setModel/modelTypeList`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.modelTypeList)
    //分类
    router.post(
        `${API_VERSION}/werewolf/setModel/classificationTypeList`,
        accCtr(AccessRouteId.app_mst),
        controller.mst.setModel.classificationTypeList,
    )
    router.post(
        `${API_VERSION}/werewolf/setModel/addClassificationType`,
        accCtr(AccessRouteId.app_mst),
        controller.mst.setModel.addClassificationType,
    )
    router.post(
        `${API_VERSION}/werewolf/setModel/delClassificationType`,
        accCtr(AccessRouteId.app_mst),
        controller.mst.setModel.delClassificationType,
    )
    //基础模型
    router.post(`${API_VERSION}/werewolf/setModel/baseModelList`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.baseModelList)
    //保存
    router.post(`${API_VERSION}/werewolf/setModel/saveModel`, accCtr(AccessRouteId.app_mst), controller.mst.setModel.saveModel)

    //显卡租赁
    router.post(`${API_VERSION}/werewolf/leaseHistory/getList`, accCtr(AccessRouteId.app_lease), controller.lease.leaseHistory.getList)
    router.post(`${API_VERSION}/werewolf/leaseHistory/searchFromList`, accCtr(AccessRouteId.app_lease), controller.lease.leaseHistory.searchFromList)
    router.post(`${API_VERSION}/werewolf/leaseHistory/insert`, accCtr(AccessRouteId.app_lease), controller.lease.leaseHistory.insert)
    router.post(`${API_VERSION}/werewolf/leaseHistory/update`, accCtr(AccessRouteId.app_lease), controller.lease.leaseHistory.update)
    router.post(`${API_VERSION}/werewolf/leaseHistory/updateType`, accCtr(AccessRouteId.app_lease), controller.lease.leaseHistory.updateType)

    //绿荫盛宴
    router.post(`${API_VERSION}/werewolf/activity/greenGala/items`, accCtr(AccessRouteId.app_wolf), controller.werewolf.activityGreenGala.findItems)
    router.post(
        `${API_VERSION}/werewolf/activity/greenGala/updateItem`,
        accCtr(AccessRouteId.app_wolf),
        controller.werewolf.activityGreenGala.updateItem,
    )
    
    router.post(
        `${API_VERSION}/werewolf/activity/greenGala/updateResultState`,
        accCtr(AccessRouteId.app_wolf),
        controller.werewolf.activityGreenGala.updateResultState,
    )

    router.post(
        `${API_VERSION}/werewolf/activity/greenGala/dates`,
        accCtr(AccessRouteId.app_wolf),
        controller.werewolf.activityGreenGala.findDateList,
    )

    router.post(
        `${API_VERSION}/werewolf/activity/greenGala/findTeamConfig`,
        accCtr(AccessRouteId.app_wolf),
        controller.werewolf.activityGreenGala.findTeamConfig,
    )

    //魔法制造
    router.post(`${API_VERSION}/werewolf/activity/magicFactory/items`, accCtr(AccessRouteId.app_wolf), controller.werewolf.activityMagicFactory.findItems)
    router.post(
        `${API_VERSION}/werewolf/activity/magicFactory/updateItem`,
        accCtr(AccessRouteId.app_wolf),
        controller.werewolf.activityMagicFactory.updateItem,
    )
    router.post(
        `${API_VERSION}/werewolf/activity/magicFactory/frameItemDicList`,
        accCtr(AccessRouteId.app_wolf),
        controller.werewolf.activityMagicFactory.findFrameItemDicList,
    )




}
